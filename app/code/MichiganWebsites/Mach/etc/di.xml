<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Config\Model\Config\TypePool">
        <arguments>
            <argument name="sensitive" xsi:type="array">
                <item name="mach/general/security_code" xsi:type="string">1</item>
            </argument>
        </arguments>
    </type>
    
    <!-- Register the shipping carrier -->
    <virtualType name="MachShippingCarrierComposite" type="Magento\Shipping\Model\Shipping">
        <arguments>
            <argument name="carriers" xsi:type="array">
                <item name="mach" xsi:type="string">MichiganWebsites\Mach\Model\Carrier\Mach</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Override Magento's tax calculation with our Mach implementation -->
    <preference for="Magento\Tax\Model\Sales\Total\Quote\Tax" type="MichiganWebsites\Mach\Model\Tax\Sales\Total\Quote\Tax" />

    <type name="Magento\Framework\Console\CommandList">
		<arguments>
			<argument name="commands" xsi:type="array">
				<item name="ProcessOrder" xsi:type="object">MichiganWebsites\Mach\Console\Command\ProcessOrder</item>
			</argument>
		</arguments>
	</type>

    <!-- Logger configuration -->
    <type name="MichiganWebsites\Mach\Cron\ProcessOrders">
        <arguments>
            <argument name="logger" xsi:type="object">MichiganWebsites\Mach\Logger\Logger</argument>
        </arguments>
    </type>
    <type name="MichiganWebsites\Mach\Model\Api\Endpoint\AbstractEndpoint">
        <arguments>
            <argument name="logger" xsi:type="object">MichiganWebsites\Mach\Logger\Logger</argument>
        </arguments>
    </type>
                        
    <virtualType name="MichiganWebsites\Mach\Logger\Handler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/mach.log</argument>
        </arguments>
    </virtualType>
    
    <virtualType name="MichiganWebsites\Mach\Logger\Logger" type="Monolog\Logger">
        <arguments>
            <argument name="name" xsi:type="string">mach</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">MichiganWebsites\Mach\Logger\Handler</item>
            </argument>
        </arguments>
    </virtualType>
    <preference for="MichiganWebsites\Mach\Api\MultishippingAddressManagementInterface" type="MichiganWebsites\Mach\Model\MultishippingCheckout\AddressManagement"/>
    <preference for="MichiganWebsites\Mach\Api\Data\AddressInterface" type="MichiganWebsites\Mach\Model\MultishippingCheckout\Address"/>
    <preference for="MichiganWebsites\Mach\Api\ValidAddressManagementInterface" type="MichiganWebsites\Mach\Model\ValidAddressManagement" />
</config>
