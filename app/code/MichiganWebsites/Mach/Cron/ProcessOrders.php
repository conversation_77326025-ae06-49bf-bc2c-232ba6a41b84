<?php

namespace MichiganWebsites\Mach\Cron;

use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Psr\Log\LoggerInterface;
use MichiganWebsites\Mach\Model\Api\MachClient;
use Magento\Customer\Api\CustomerRepositoryInterface;

class ProcessOrders
{
    /**
     * @var CollectionFactory
     */
    private $orderCollectionFactory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var MachClient
     */
    private $machClient;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * @param CollectionFactory $orderCollectionFactory
     * @param LoggerInterface $logger
     * @param MachClient $machClient
     * @param CustomerRepositoryInterface $customerRepository
     * @param OrderRepositoryInterface $orderRepository
     */
    public function __construct(
        CollectionFactory $orderCollectionFactory,
        LoggerInterface $logger,
        MachClient $machClient,
        CustomerRepositoryInterface $customerRepository,
        OrderRepositoryInterface $orderRepository
    ) {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->logger = $logger;
        $this->machClient = $machClient;
        $this->customerRepository = $customerRepository;
        $this->orderRepository = $orderRepository;
    }

    /**
     * Execute cron job
     *
     * @return void
     */
    public function execute()
    {
        try {
            $this->logger->info('Starting MACH order processing cron job');
            
            // Get orders from the last 24 hours that haven't been processed
            $yesterday = date('Y-m-d H:i:s', strtotime('-1 day'));
            $collection = $this->orderCollectionFactory->create();
            $collection->addFieldToFilter('created_at', ['gteq' => $yesterday])
                ->addFieldToFilter('mach_order_result', ['null' => true]);
            
            $this->logger->info('Found ' . $collection->getSize() . ' orders to process');

            foreach ($collection as $order) {
                try {
                    $result = $this->machClient->addMagentoOrder($order, $order->getIncrementId());
                    if (is_object($result)) {
                        $result = json_decode(json_encode($result), true);
                    }
                    if (isset($result['ADD_ORDER_OUT']['CustNumber'])) {
                        // Save customer ID if it's a new customer
                        $customer = null;
                        if ($order->getCustomerId()) {
                            $customer = $this->customerRepository->getById($order->getCustomerId());
                        }
                        if ($customer && !$customer->getCustomAttribute('mach_customer_id')) {
                            $customer->setCustomAttribute('mach_customer_id', $result['ADD_ORDER_OUT']['CustNumber']);
                            $this->customerRepository->save($customer);
                        }
                    }
                    $order->setMachOrderId($result['OrderNumber']);
                    $order->setMachOrderResult($result['ERROR_OUT']['ErrorNumber'] == 0?'Pass':'Fail');
                    $this->orderRepository->save($order);
                } catch (\Exception $e) {
                    $this->logger->error(
                        'Error processing order #' . $order->getIncrementId() . ': ' . $e->getMessage(),
                        ['exception' => $e]
                    );
                }
            }
            
            $this->logger->info('Completed MACH order processing cron job');
        } catch (\Exception $e) {
            $this->logger->critical('Error in MACH order processing cron: ' . $e->getMessage(), ['exception' => $e]);
        }
    }
}
