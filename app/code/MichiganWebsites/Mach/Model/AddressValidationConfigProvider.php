<?php

namespace MichiganWebsites\Mach\Model;

use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Store\Model\StoreManagerInterface;
use MichiganWebsites\Mach\Helper\Config;

class AddressValidationConfigProvider implements ConfigProviderInterface
{
    /**
     * @var Config
     */
    private $config;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @param Config $config
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        Config $config,
        StoreManagerInterface $storeManager
    ) {
        $this->config = $config;
        $this->storeManager = $storeManager;
    }

    /**
     * {@inheritdoc}
     */
    public function getConfig()
    {
        $store = $this->storeManager->getStore();
        
        return [
            'machAddressValidation' => [
                'enabled' => $this->config->isAddressValidationEnabled($store),
                'countriesEnabled' => 'US'
            ]
        ];
    }
}