<?php

namespace MichiganWebsites\Mach\Model\Api\Endpoint;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Model\Order;
use Magento\Framework\Exception\NoSuchEntityException;

class AddOrder extends AbstractEndpoint
{
    /**
     * @inheritdoc
     */
    protected function getActionCode()
    {
        return '1';
    }

    /**
     * @inheritdoc
     */
    protected function getMethodName()
    {
        return 'ADD_ORDER';
    }

    /**
     * Add an order to Mach
     *
     * @param array $orderData
     * @return mixed
     * @throws LocalizedException
     */
    public function addOrder(array $orderData)
    {
        $params = [
            'ReferenceNumber' => '',
            'ADD_ORDER_IN' => $orderData
        ];

        return $this->execute($params);
    }

    /**
     * Add a Magento order to Mach
     *
     * @param OrderInterface $order
     * @param string $referenceNumber Optional reference number
     * @return mixed
     * @throws LocalizedException
     */
    public function addMagentoOrder(OrderInterface $order, $referenceNumber = '')
    {
        // Always use the increment ID as the reference number if not provided
        if (empty($referenceNumber)) {
            $referenceNumber = $order->getIncrementId();
        }

        $orderData = $this->formatOrderData($order);

        $params = [
            'ReferenceNumber' => $referenceNumber,
            'ADD_ORDER_IN' => $orderData
        ];

        return $this->execute($params);
    }

    /**
     * Format Magento order data for Mach API
     *
     * @param OrderInterface $order
     * @return array
     */
    protected function formatOrderData(OrderInterface $order)
    {
        $billingAddress = $order->getBillingAddress();
        /** @var \Magento\Sales\Model\Order\Address $shippingAddress */
        $shippingAddress = $order->getShippingAddress() ?: $billingAddress;

        $orderData = [
            // Customer information
            'CustNumber' => $this->getCustomerMachId($order),
            'BillName' => $billingAddress->getName(),
            'BillAdd1' => $billingAddress->getStreetLine(1),
            'BillAdd2' => $billingAddress->getStreetLine(2) ?: '',
            'BillCity' => $billingAddress->getCity(),
            'BillState' => $billingAddress->getRegionCode() ?: '',  // Ensure not null
            'BillZip' => $billingAddress->getPostcode(),
            'BillCounty' => '',
            'BillCountry' => $billingAddress->getCountryId(),
            'BillPhone' => $billingAddress->getTelephone(),
            'BillFax' => '',
            'BillEmail' => $order->getCustomerEmail(),
            'BillPIN' => '',
            'BillContact' => '',
            'BillTitle' => '',
            'BillMailList' => 'N',
            'BillRentList' => 'N',
            'BillEmailList' => 'Y',
            'BillOpen1' => '',
            'BillOpen2' => '',
            'BillOpen3' => '',
            'BillOpen4' => '',
            'BillOpen5' => '',
            'BillOpen6' => '',
            'BillOpen7' => '',
            'BillOpen8' => '',
            'BillOpen9' => '',
            'BillOpen10' => '',
            'BillOpen11' => '',
            'BillOpen12' => '',
            'BillOpen13' => '',
            'BillOpen14' => '',
            'BillOpen15' => '',
            'BillUpdateFlag' => 'Y',

            // Tax information
            'TaxExemptNumber' => '',
            'SoldAccount' => '',
            'ShipAccount' => '',

            // Shipping information
            'ShipName' => $shippingAddress->getName(),
            'ShipAdd1' => $shippingAddress->getStreetLine(1),
            'ShipAdd2' => $shippingAddress->getStreetLine(2) ?: '',
            'ShipCity' => $shippingAddress->getCity(),
            'ShipState' => $shippingAddress->getRegionCode() ?: '',  // Ensure not null
            'ShipZip' => $shippingAddress->getPostcode(),
            'ShipCounty' => '',
            'ShipCountry' => $shippingAddress->getCountryId(),
            'ShipPhone' => $shippingAddress->getTelephone(),
            'OrderEmailAddress' => $order->getCustomerEmail(),
            'Resubmitted' => 'N',

            // Payment information - using CK as requested
            'PaymentTerms' => 'CK',

            // Order information
            'OrderType' => 'WEB',
            'Channel' => 'WEB',
            'OrderDate' => $order->getCreatedAt() ? date('Y-m-d', strtotime($order->getCreatedAt())) : date('Y-m-d'),
            'OrderTime' => $order->getCreatedAt() ? date('H:i:s', strtotime($order->getCreatedAt())) : date('H:i:s'),
            'ShipDate' => '', // todo: assign the selected shipping window attribute
            'UpdateAccount' => 'Y',
            'ShipVia' => $order->getShippingMethod() ?: '',
            'ArrivalDate' => '',

            // Order totals
            'MerchAmt' => (float)$order->getSubtotal(),
            'ShipAmt' => (float)$order->getShippingAmount(),
            'AddAmt' => 0.00,
            'CODAmt' => 0.00,
            'ClubDiscAmt' => 0.00,
            'GCNum1' => '',
            'GCAmt1' => 0.00,
            'GCNum2' => '',
            'GCAmt2' => 0.00,
            'GCNum3' => '',
            'GCAmt3' => 0.00,
            'GCNum4' => '',
            'GCAmt4' => 0.00,
            'GCNum5' => '',
            'GCAmt5' => 0.00,
            'TaxAmt' => (float)$order->getTaxAmount(),
            'TotalAmt' => (float)$order->getGrandTotal(),
            'TaxCode' => '',

            // Payment information
            // 'PaymentTerms' => $this->mapPaymentMethod($order->getPayment() ? $order->getPayment()->getMethod() : ''),
            'CCN' => '',
            'CCEXP' => '',
            'NameOnCard' => '',
            'CardAddress' => '',
            'CardZip' => '',
            'CVV' => '',
            'CheckNumber' => '',
            'CheckAmt' => '',
            'PONumber' => '', //$order->getPayment() && $order->getPayment()->getPoNumber() ? $order->getPayment()->getPoNumber() : '',

            // Shipping method
            'ShipVia' => $order->getShippingMethod() ?: '', // todo: this should be the method code like GRD or 2DA
            'FreightCode' => '',
            'Filler1' => '',
            'Filler2' => '',

            // Order metadata
            'KeyCode' => 'WEB',
            'OrderType' => 'WEB',
            'Channel' => 'WEB',
            'MachOrderNumber' => '',
            'MSOrderNumber' => '', //$order->getIncrementId(), // todo: this is used for multi-address orders
            'MSCount' => '',
            'Attention' => '',

            // Comments
            'CommentLine' => [
                'Comments' => $order->getCustomerNote() ?: ''
            ],
            'NoteLine' => [
                'InternalNotes' => ''
            ],

            // Additional information
            'PriceLvl' => 'RETAIL',
            'HSC' => '',
            'ResCom' => 'RES',
            'OrderGiftFlag' => 'N',
            'OrderGiftMsgLine' => [
                'OrderGiftMsg' => '' // todo: get order level gift message (to, from, body)
            ],

            // Open codes
            'OrderOpenCode1' => '',
            'OrderOpenCode2' => '',
            'OrderOpenCode3' => '',
            'OrderOpenCode4' => '',
            'OrderOpenCode5' => '',
            'OrderOpenCode6' => '',
            'OrderOpenCode7' => '',
            'OrderOpenCode8' => '',
            'OrderOpenCode9' => '',
            'OrderOpenCode10' => '',
            'OrderOpenCode11' => '',
            'OrderOpenCode12' => '',
            'OrderOpenCode13' => '',
            'OrderOpenCode14' => '',
            'OrderOpenCode15' => '',

            // IP and session information
            'IPAddress' => '',
            'SignifydSession' => '',

            // Reserved fields
            'Reserved20' => '',
            'Reserved21' => '',
            'Reserved22' => '',
            'Reserved23' => '',
            'Reserved24' => '',
            'Reserved25' => '',
            'Reserved26' => '',

            // Card transaction information
            'CardTransactionID' => '', // $order->getPayment() ? $order->getPayment()->getLastTransId() : '',
            'CardAuthCode' => '',
            'CardAuthAmount' => '', //(float)$order->getGrandTotal(),
            'CardAuthDate' => '', //$order->getCreatedAt() ? date('Y-m-d', strtotime($order->getCreatedAt())) : date('Y-m-d'),
            'CardPartialCard' => '',
            'CardExpDate' => '',
            'CardAVSCode' => '',
            'CardCVVCode' => '',
            'CardholderNameOnCard' => '',
            'CardholderAddress' => '',
            'CardholderZip' => '',
            'CardOpen1' => '',
            'CardOpen2' => '',

            // Additional charges
            'AddCharges' => [
                'AddCode' => '',
                'AddAmount' => 0.00,
                'AddOverride' => 'N'
            ],

            // Billing advice
            'BillAdv' => 'N',
            'BillAdvDate' => '',
            'BillAdvDPI' => '',
            'BillAdvDI' => '',
            'BillAdvOpen1' => '',
            'BillAdvOpen2' => '',

            // Shipping advice
            'ShipAdv' => 'N',
            'ShipAdvDate' => '',
            'ShipAdvDPI' => '',
            'ShipAdvDI' => '',
            'ShipAdvOpen1' => '',
            'ShipAdvOpen2' => '',

            // PayPal information
            'PayPalTransactionID' => '',
            'PayPalAmount' => 0.00,
            'PayPalOpen1' => '',
            'PayPalOpen2' => '',

            // COD information
            'CODCode' => '',

            // Order items
            'OrderItem' => $this->formatOrderItems($order)
        ];

        return $orderData;
    }

    /**
     * Format order items for MACH API
     *
     * @param Order $order
     * @return array
     */
    private function formatOrderItems(Order $order)
    {
        $items = [];

        foreach ($order->getAllVisibleItems() as $item) {
            $items[] = [
                'ItemNumber' => $item->getSku(),
                'QtyOrdered' => (int)$item->getQtyOrdered(),
                'ItemType' => 'REGULAR',
                'Price' => $item->getPrice(),
                'DropShip' => 'N',
                'DescLine' => [
                    'ItemDesc' => $item->getName()
                ],
                'GiftCardNum' => '',
                'ItemContFlag' => 'N',
                'ItemContNumber' => '',
                'ItemContInterval' => '',
                'PersCode' => '',
                'Personalization' => [
                    'PersData1' => '',
                    'PersData2' => '',
                    'PersData3' => '',
                    'PersData4' => '',
                    'PersData5' => '',
                    'PersData6' => '',
                    'PersData7' => '',
                    'PersData8' => '',
                    'PersData9' => '',
                    'PersData10' => '',
                    'PersData11' => '',
                    'PersData12' => '',
                    'PersData13' => '',
                    'PersData14' => '',
                    'PersData15' => '',
                    'PersData16' => '',
                    'PersData17' => '',
                    'PersData18' => ''
                ],
                'CatalogCode' => '',
                'GiftMsgLine' => [
                    'ItemGiftMsg' => ''
                ],
                'Reserved2' => '',
                'Reserved3' => '',
                'Reserved4' => '',
                'Reserved5' => '',
                'GiftCardInfo' => [
                    'GiftCardFromName' => '',
                    'GiftCardToName' => '',
                    'GiftCardToEmail' => '',
                    'GiftCardToMessage' => '',
                    'GiftCardToDeliveryDate' => '',
                    'GiftCardReserved1' => '',
                    'GiftCardReserved2' => ''
                ]
            ];
        }

        return $items;
    }

    /**
     * Map Magento payment method to Mach payment type
     *
     * @param string $method
     * @return string
     */
    protected function mapPaymentMethod($method)
    {
        $map = [
            'checkmo' => 'CHECK',
            'cashondelivery' => 'COD',
            'purchaseorder' => 'PO',
            'banktransfer' => 'WIRE',
            // Add more mappings as needed
        ];

        // Default to credit card for most payment methods
        if (isset($map[$method])) {
            return $map[$method];
        }

        // Default to credit card
        return 'CC';
    }

    /**
     * Format address for API request
     *
     * @param array $address
     * @return array
     */
    protected function formatAddress(array $address)
    {
        // Ensure street is an array and has at least one element
        $street = isset($address['street']) ? $address['street'] : [];
        if (is_string($street)) {
            $street = explode("\n", $street);
        }

        $street1 = isset($street[0]) ? $street[0] : '';
        $street2 = isset($street[1]) ? $street[1] : '';

        return [
            'Name' => isset($address['firstname']) && isset($address['lastname'])
                ? $address['firstname'] . ' ' . $address['lastname']
                : (isset($address['name']) ? $address['name'] : ''),
            'Company' => isset($address['company']) ? $address['company'] : '',
            'Address1' => $street1,
            'Address2' => $street2,
            'City' => isset($address['city']) ? $address['city'] : '',
            'State' => isset($address['region']) ? $address['region'] : '',
            'Zip' => isset($address['postcode']) ? $address['postcode'] : '',
            'Country' => isset($address['country_id']) ? $address['country_id'] : '',
            'Phone' => isset($address['telephone']) ? $address['telephone'] : '',
            'Email' => isset($address['email']) ? $address['email'] : ''
        ];
    }

    /**
     * Get customer's MACH ID if available
     *
     * @param Order $order
     * @return string
     */
    private function getCustomerMachId(Order $order)
    {
        // If customer is not logged in, return empty string
        if (!$order->getCustomerId()) {
            return '';
        }

        try {
            $customer = $this->customerRepository->getById($order->getCustomerId());
            $machCustomerId = $customer->getCustomAttribute('mach_customer_id');

            return $machCustomerId ? $machCustomerId->getValue() : '';
        } catch (NoSuchEntityException $e) {
            $this->logger->warning('Customer not found for order #' . $order->getIncrementId());
            return '';
        }
    }
}
