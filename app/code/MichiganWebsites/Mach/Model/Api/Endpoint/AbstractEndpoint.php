<?php

namespace MichiganWebsites\Mach\Model\Api\Endpoint;

use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\ScopeInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Psr\Log\LoggerInterface;
use Magento\Directory\Model\RegionFactory;

abstract class AbstractEndpoint
{
    const XML_PATH_CATALOG_TYPE = 'mach/general/catalog_type';
    const XML_PATH_WSDL_URL = 'mach/general/wsdl_url';
    const XML_PATH_SECURITY_CODE = 'mach/general/security_code';

    /**
     * @var \SoapClient
     */
    protected $client;

    /**
     * @var string
     */
    protected $securityCode;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var RegionFactory
     */
    protected $regionFactory;

    /**
     * @var string
     */
    protected $wsdlFile = 'machws.wsdl';

    /**
     * AbstractEndpoint constructor.
     *
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @throws LocalizedException
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger,
        RegionFactory $regionFactory
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->customerRepository = $customerRepository;
        $this->logger = $logger;
        $this->regionFactory = $regionFactory;
        $this->securityCode = $this->getSecurityCode();
        $this->initSoapClient();
    }

    /**
     * Initialize the SOAP client
     *
     * @return void
     * @throws LocalizedException
     */
    protected function initSoapClient()
    {
        try {
            $wsdlPath = $this->getWsdlPath();
            $options = [
                'trace' => 1,
                'exceptions' => true,
                'cache_wsdl' => WSDL_CACHE_NONE
            ];
            
            $this->client = new \SoapClient($wsdlPath, $options);
            $wsdlUrl = $this->getWsdlUrl();
            if (strpos($wsdlPath, 'addressws.wsdl') !== false) {
                $wsdlUrl = str_replace('/machws', '/addressws', $wsdlUrl);
            }
            $this->client->__setLocation($wsdlUrl);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Failed to initialize SOAP client: %1', $e->getMessage()));
        }
    }

    /**
     * Get the WSDL file path
     *
     * @return string
     */
    protected function getWsdlPath()
    {
        // Get module directory from composer or Magento
        $moduleDir = dirname(dirname(dirname(__DIR__)));
        return $moduleDir . '/etc/wsdl/' . $this->getWsdlFile();
    }

    /**
     * Get the WSDL file name
     *
     * @return string
     */
    protected function getWsdlFile()
    {
        return $this->wsdlFile;
    }

    /**
     * Get the security code from configuration
     *
     * @return string
     */
    protected function getSecurityCode()
    {
        return $this->scopeConfig->getValue('mach/general/security_code');
    }

    /**
     * Get the WSDL URL from configuration
     *
     * @return string
     */
    protected function getWsdlUrl()
    {
        return $this->scopeConfig->getValue('mach/general/wsdl_url');
    }

    /**
     * Get the configured catalog type
     *
     * @param int|string|null $storeId
     * @return string
     */
    protected function getCatalogType($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CATALOG_TYPE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: '2025'; // Default value if not set
    }

    /**
     * Get the action code for this endpoint
     *
     * @return string
     */
    abstract protected function getActionCode();

    /**
     * Get the method name for this endpoint
     *
     * @return string
     */
    abstract protected function getMethodName();

    /**
     * Execute the endpoint request
     *
     * @param array $params
     * @return mixed
     * @throws LocalizedException
     */
    public function execute(array $params = [])
    {
        try {
            // Add common parameters
            $params['SecurityCode'] = $this->securityCode;
            
            // Check if the method is GET_FREIGHT which uses 'Action' instead of 'ActionCode'
            if ($this->getMethodName() === 'GET_FREIGHT') {
                $params['Action'] = $this->getActionCode();
            } else {
                $params['ActionCode'] = $this->getActionCode();
            }

            // Get the method name to call
            $methodName = $this->getMethodName();

            // Execute the SOAP call
            $response = $this->client->$methodName($params);

            $this->logger->debug(print_r(['endpoint' => $methodName, "request" => $params, "response" => $response], true));

            return $response;
        } catch (\SoapFault $e) {
            throw new LocalizedException(__('SOAP Error: %1', $e->getMessage()));
        } catch (\Exception $e) {
            throw new LocalizedException(__('%1 endpoint failed: %2', $this->getMethodName(), $e->getMessage()));
        }
    }
}
