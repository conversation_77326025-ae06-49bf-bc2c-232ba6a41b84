<?php

namespace MichiganWebsites\Mach\Console\Command;

use Magento\Framework\App\State;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use MichiganWebsites\Mach\Model\Api\MachClient;
use Magento\Customer\Api\CustomerRepositoryInterface;

class ProcessOrder extends Command
{
    /**
     * @var State
     */
    private $appState;

    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * @var MachClient
     */
    private $machClient;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @param State $appState
     * @param OrderRepositoryInterface $orderRepository
     * @param MachClient $machClient
     * @param CustomerRepositoryInterface $customerRepository
     * @param string|null $name
     */
    public function __construct(
        State $appState,
        OrderRepositoryInterface $orderRepository,
        MachClient $machClient,
        CustomerRepositoryInterface $customerRepository,
        string $name = null
    ) {
        parent::__construct($name);
        $this->appState = $appState;
        $this->orderRepository = $orderRepository;
        $this->machClient = $machClient;
        $this->customerRepository = $customerRepository;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName('mach:process:order')
            ->setDescription('Process a specific order and send it to MACH')
            ->addArgument(
                'order_id',
                InputArgument::REQUIRED,
                'Order ID or Increment ID'
            );

        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->appState->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Area code already set
        }

        $orderId = $input->getArgument('order_id');
        $output->writeln("<info>Processing order: $orderId</info>");

        try {
            $order = $this->getOrder($orderId);
            if (!$order) {
                $output->writeln("<error>Order not found: $orderId</error>");
                return 1;
            }

            $result = $this->machClient->addMagentoOrder($order, $order->getIncrementId());
            if (is_object($result)) {
                $result = json_decode(json_encode($result), true);
            }
            if (isset($result['ADD_ORDER_OUT']['CustNumber'])) {
                // Save customer ID if it's a new customer
                $customer = null;
                if ($order->getCustomerId()) {
                    $customer = $this->customerRepository->getById($order->getCustomerId());
                }
                if ($customer && !$customer->getCustomAttribute('mach_customer_id')) {
                    $customer->setCustomAttribute('mach_customer_id', $result['ADD_ORDER_OUT']['CustNumber']);
                    $this->customerRepository->save($customer);
                }
            }
            $order->setMachOrderId($result['OrderNumber']);
            $order->setMachOrderResult($result['ERROR_OUT']['ErrorNumber'] == 0?'Pass':'Fail');
            $this->orderRepository->save($order);

            $output->writeln("<info>Order processed successfully</info>");
            return 0;
        } catch (\Exception $e) {
            $output->writeln("<error>Error processing order: " . $e->getMessage() . "</error>");
            return 1;
        }
    }

    /**
     * Get order by ID or increment ID
     *
     * @param string $orderId
     * @return \Magento\Sales\Api\Data\OrderInterface|null
     */
    private function getOrder($orderId)
    {
        try {
            // Try to load by order ID
            return $this->orderRepository->get($orderId);
        } catch (LocalizedException $e) {
            // Try to load by increment ID
            $searchCriteria = $this->orderRepository->create()->addFieldToFilter('increment_id', $orderId);
            $orders = $this->orderRepository->getList($searchCriteria)->getItems();
            return reset($orders) ?: null;
        }
    }
}
