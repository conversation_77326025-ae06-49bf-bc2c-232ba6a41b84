define([
    'jquery',
    'ko',
    'MichiganWebsites_Mach/js/model/address-model',
    'MichiganWebsites_Mach/js/view/address-validation-form',
    'Magento_Checkout/js/model/checkout-data-resolver',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/action/select-billing-address',
    'Magento_Checkout/js/action/create-billing-address',
    'text!MichiganWebsites_Mach/template/modal/modal-popup.html'
], function (
    $,
    ko,
    addressModel,
    addressValidationForm,
    checkoutDataResolver,
    quote,
    selectBillingAddress,
    createBillingAddress,
    popupTpl
) {

    $.widget('mage.checkoutBillingAddressValidationModal', $.mage.modal, {
        validationContainer: '.billingValidationModal .modal-content > div',
        formSelector: '.billing-address-form form',
        options: {
            title: $.mage.__('Verify Your Billing Address'),
            modalClass: 'billingValidationModal',
            focus: '.billingValidationModal .action-primary',
            responsive: true,
            closeText: $.mage.__('Close'),
            popupTpl: popupTpl,
            buttons: [
                {
                    text: $.mage.__('Edit Address'),
                    class: 'action-secondary action-dismiss',
                    click: function () {
                        this.editAddress();
                    }
                },
                {
                    text: $.mage.__('Save Address'),
                    class: 'action-primary action primary',
                    click: function () {
                        if (addressModel.isDifferent()) {
                            addressModel.selectedAddress()['save_in_address_book'] = quote.billingAddress().saveInAddressBook;
                            selectBillingAddress(createBillingAddress(addressModel.selectedAddress()));
                            checkoutDataResolver.applyBillingAddress();
                            addressValidationForm.updateFormFields(this.formSelector);
                        }
                        window.checkoutConfig.machAddressValidation.isAddressValid = true;
                        this.clickNativePlaceOrder();
                        this.closeModal();
                    }
                }
            ]
        },

        _create: function () {
            this._super();
            addressValidationForm.bindTemplate(this.validationContainer, this.options, 'MichiganWebsites_Mach/baseValidateAddress');
        },

        openModal: function () {
            this._super();
            var self = this;
            $(this.validationContainer + " .edit-address").on('click', function () {
                self.editAddress();
            });
        },

        closeModal: function () {
            this._super();
        },

        editAddress: function () {
            var self = this;
            self.clickNativeEditBillingAddress();
            window.checkoutConfig.machAddressValidation.isAddressValid = false;
            self.closeModal();
        },

        clickNativePlaceOrder: function () {
            $('.payment-method._active button[type=submit].checkout').click();
        },

        clickNativeEditBillingAddress: function () {
            $('.payment-method._active .action-edit-address').click();
        }
    });

    return $.mage.checkoutBillingAddressValidationModal;
});
